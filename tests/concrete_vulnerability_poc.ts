import { BN } from "@coral-xyz/anchor";
import { PublicKey, Keypair } from "@solana/web3.js";

/**
 * Concrete Proof of Concept for Slippage Protection Vulnerabilities
 * 
 * This PoC demonstrates the actual vulnerabilities in the dynamic bonding curve
 * by showing how trades can pass slippage checks when they shouldn't.
 */

describe("Slippage Protection Vulnerabilities PoC", () => {
  
  /**
   * PoC 1: BUY Vulnerability - Unscaled Token Comparison
   * 
   * Demonstrates how the current implementation compares unscaled amounts
   * against user expectations, leading to insufficient slippage protection.
   */
  it("demonstrates BUY vulnerability - unscaled token comparison", async () => {
    console.log("=== BUY VULNERABILITY PoC ===");
    
    // Setup: User wants to buy tokens with proper slippage protection
    const solInput = new BN(1_000_000); // 1 SOL in lamports
    const tokenDecimals = 6;
    const expectedTokens = 1000; // User expects 1000 tokens
    const expectedTokensScaled = new BN(expectedTokens * Math.pow(10, tokenDecimals)); // 1,000,000,000
    
    console.log(`User input: ${solInput.toString()} lamports (1 SOL)`);
    console.log(`Expected tokens: ${expectedTokens}`);
    console.log(`Expected tokens scaled: ${expectedTokensScaled.toString()}`);
    
    // Simulate current vulnerable behavior
    const mockSwapResult = {
      output_amount: new BN(1000), // Pool returns unscaled amount
      // ... other fields
    };
    
    // VULNERABILITY: Current code does this comparison
    const currentCheck = mockSwapResult.output_amount.gte(expectedTokensScaled);
    console.log(`Current vulnerable check: ${mockSwapResult.output_amount.toString()} >= ${expectedTokensScaled.toString()} = ${currentCheck}`);
    
    // This should FAIL but might PASS if user mistakenly uses unscaled minimum
    const vulnerableMinimum = new BN(1000); // Unscaled
    const vulnerableCheck = mockSwapResult.output_amount.gte(vulnerableMinimum);
    console.log(`Vulnerable scenario: ${mockSwapResult.output_amount.toString()} >= ${vulnerableMinimum.toString()} = ${vulnerableCheck}`);
    
    if (vulnerableCheck) {
      console.log("🚨 VULNERABILITY CONFIRMED: Trade passes with unscaled comparison");
      console.log(`User expects: ${expectedTokens} tokens`);
      console.log(`User receives: ${mockSwapResult.output_amount.toNumber() / Math.pow(10, tokenDecimals)} tokens`);
      console.log(`Loss: ${expectedTokens - (mockSwapResult.output_amount.toNumber() / Math.pow(10, tokenDecimals))} tokens`);
    }
  });

  /**
   * PoC 2: SELL Vulnerability - Gross vs Net Amount Check
   * 
   * Demonstrates how the current implementation checks gross amounts
   * instead of net amounts after fees.
   */
  it("demonstrates SELL vulnerability - gross vs net amount check", async () => {
    console.log("\n=== SELL VULNERABILITY PoC ===");
    
    // Setup: User wants to sell tokens for SOL with slippage protection
    const tokensToSell = new BN(1000 * Math.pow(10, 6)); // 1000 tokens with 6 decimals
    const minSolExpected = new BN(900_000); // User expects at least 0.9 SOL net
    
    console.log(`Tokens to sell: ${tokensToSell.toString()}`);
    console.log(`Minimum SOL expected (net): ${minSolExpected.toString()} lamports`);
    
    // Simulate swap result with fees
    const mockSwapResult = {
      included_fee_input_amount: new BN(1_000_000), // 1 SOL gross
      excluded_fee_input_amount: new BN(950_000),   // 0.95 SOL before fees
      trading_fee: new BN(30_000),    // 0.03 SOL
      protocol_fee: new BN(15_000),   // 0.015 SOL  
      referral_fee: new BN(5_000),    // 0.005 SOL
    };
    
    const totalFees = mockSwapResult.trading_fee
      .add(mockSwapResult.protocol_fee)
      .add(mockSwapResult.referral_fee);
    
    const netSolAmount = mockSwapResult.included_fee_input_amount.sub(totalFees);
    
    console.log(`Gross SOL amount: ${mockSwapResult.included_fee_input_amount.toString()} lamports`);
    console.log(`Total fees: ${totalFees.toString()} lamports`);
    console.log(`Net SOL amount: ${netSolAmount.toString()} lamports`);
    
    // VULNERABILITY: Current code checks gross amount
    const currentCheck = mockSwapResult.included_fee_input_amount.lte(minSolExpected.add(totalFees));
    console.log(`Current vulnerable check (gross): ${mockSwapResult.included_fee_input_amount.toString()} <= ${minSolExpected.add(totalFees).toString()} = ${currentCheck}`);
    
    // What should be checked: net amount
    const properCheck = netSolAmount.gte(minSolExpected);
    console.log(`Proper check (net): ${netSolAmount.toString()} >= ${minSolExpected.toString()} = ${properCheck}`);
    
    if (currentCheck && !properCheck) {
      console.log("🚨 VULNERABILITY CONFIRMED: Trade passes gross check but fails net check");
      console.log(`User expects: ${minSolExpected.toNumber() / 1_000_000} SOL net`);
      console.log(`User receives: ${netSolAmount.toNumber() / 1_000_000} SOL net`);
      console.log(`Loss: ${(minSolExpected.toNumber() - netSolAmount.toNumber()) / 1_000_000} SOL`);
    }
  });

  /**
   * PoC 3: Combined Exploit Scenario
   * 
   * Shows how an attacker could exploit both vulnerabilities
   * in a coordinated attack.
   */
  it("demonstrates combined exploit scenario", async () => {
    console.log("\n=== COMBINED EXPLOIT SCENARIO ===");
    
    console.log("1. Attacker identifies vulnerable slippage calculations");
    console.log("2. Attacker front-runs legitimate user transactions");
    console.log("3. Attacker manipulates pool state to create unfavorable conditions");
    console.log("4. User's transaction executes with inadequate slippage protection");
    console.log("5. User receives less value than expected");
    console.log("6. Attacker profits from the price difference");
    
    // Example exploit flow:
    const legitimateUserTrade = {
      type: "BUY",
      input: new BN(10_000_000), // 10 SOL
      expectedOutput: new BN(10000 * Math.pow(10, 6)), // 10,000 tokens scaled
      vulnerableMinimum: new BN(10000), // User mistakenly uses unscaled
    };
    
    const attackerManipulation = {
      frontRunTrade: new BN(5_000_000), // 5 SOL to manipulate price
      priceImpact: 0.05, // 5% price impact
    };
    
    const resultingTrade = {
      actualOutput: new BN(9500), // User gets 9,500 unscaled tokens
      scaledOutput: new BN(9500 * Math.pow(10, 6)), // 9,500,000,000 scaled
    };
    
    console.log(`\nLegitimate user trade:`);
    console.log(`- Input: ${legitimateUserTrade.input.toNumber() / 1_000_000} SOL`);
    console.log(`- Expected: ${legitimateUserTrade.expectedOutput.toNumber() / Math.pow(10, 6)} tokens`);
    console.log(`- Vulnerable minimum: ${legitimateUserTrade.vulnerableMinimum.toString()} (unscaled)`);
    
    console.log(`\nAfter attacker manipulation:`);
    console.log(`- Actual output: ${resultingTrade.actualOutput.toString()} (unscaled)`);
    console.log(`- Scaled output: ${resultingTrade.scaledOutput.toNumber() / Math.pow(10, 6)} tokens`);
    
    const vulnerableCheckPasses = resultingTrade.actualOutput.gte(legitimateUserTrade.vulnerableMinimum);
    const properCheckPasses = resultingTrade.scaledOutput.gte(legitimateUserTrade.expectedOutput);
    
    console.log(`\nVulnerable check passes: ${vulnerableCheckPasses}`);
    console.log(`Proper check passes: ${properCheckPasses}`);
    
    if (vulnerableCheckPasses && !properCheckPasses) {
      console.log("🚨 EXPLOIT SUCCESSFUL: User loses value due to inadequate slippage protection");
      const expectedTokens = legitimateUserTrade.expectedOutput.toNumber() / Math.pow(10, 6);
      const actualTokens = resultingTrade.scaledOutput.toNumber() / Math.pow(10, 6);
      console.log(`User expected: ${expectedTokens} tokens`);
      console.log(`User received: ${actualTokens} tokens`);
      console.log(`User loss: ${expectedTokens - actualTokens} tokens (${((expectedTokens - actualTokens) / expectedTokens * 100).toFixed(2)}%)`);
    }
  });

  /**
   * PoC 4: Fee Calculation Edge Cases
   * 
   * Demonstrates edge cases where fee calculations can be exploited.
   */
  it("demonstrates fee calculation edge cases", async () => {
    console.log("\n=== FEE CALCULATION EDGE CASES ===");
    
    // High fee scenario
    const highFeeScenario = {
      grossAmount: new BN(1_000_000), // 1 SOL
      tradingFee: new BN(100_000),    // 0.1 SOL (10%)
      protocolFee: new BN(50_000),    // 0.05 SOL (5%)
      referralFee: new BN(25_000),    // 0.025 SOL (2.5%)
    };
    
    const totalHighFees = highFeeScenario.tradingFee
      .add(highFeeScenario.protocolFee)
      .add(highFeeScenario.referralFee);
    
    const netHighFeeAmount = highFeeScenario.grossAmount.sub(totalHighFees);
    
    console.log("High fee scenario:");
    console.log(`- Gross amount: ${highFeeScenario.grossAmount.toNumber() / 1_000_000} SOL`);
    console.log(`- Total fees: ${totalHighFees.toNumber() / 1_000_000} SOL (${(totalHighFees.toNumber() / highFeeScenario.grossAmount.toNumber() * 100).toFixed(1)}%)`);
    console.log(`- Net amount: ${netHighFeeAmount.toNumber() / 1_000_000} SOL`);
    
    // User expects 0.9 SOL minimum
    const userMinimum = new BN(900_000);
    const grossCheck = highFeeScenario.grossAmount.lte(userMinimum.add(totalHighFees));
    const netCheck = netHighFeeAmount.gte(userMinimum);
    
    console.log(`Gross check (vulnerable): ${grossCheck}`);
    console.log(`Net check (proper): ${netCheck}`);
    
    if (grossCheck && !netCheck) {
      console.log("🚨 HIGH FEE EXPLOIT: User pays excessive fees due to gross amount check");
    }
  });
});

/**
 * Summary of Vulnerabilities Demonstrated:
 * 
 * 1. BUY Operations: Unscaled token comparison allows trades with insufficient tokens
 * 2. SELL Operations: Gross amount check allows trades with excessive fees  
 * 3. Combined Exploits: Attackers can manipulate both vulnerabilities
 * 4. Edge Cases: High fee scenarios amplify the vulnerabilities
 * 
 * Impact: Users lose value due to inadequate slippage protection
 * Risk: HIGH - Direct financial loss possible
 */
